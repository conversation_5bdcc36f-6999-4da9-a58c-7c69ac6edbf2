package cn.genn.orch.tfs.application.dto;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.orch.tfs.infrastructure.enums.StatusEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * TfsDayFillLogDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ContentRowHeight(20) // 设置行高
public class TfsDayFillLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    @ExcelProperty(value = "id", index = 0) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private Long id;

    @ApiModelProperty(value = "飞书open_id")
    @ExcelProperty(value = "openId", index = 1) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private String openId;

    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名字", index = 2) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private String name;

    @ApiModelProperty(value = "手机号")
    @ExcelProperty(value = "手机号", index = 3) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private String telephone;

    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "填报日期", index = 4) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private LocalDate sendDate;

    @ApiModelProperty(value = "0:未填报；1：已填报")
    private StatusEnum status;

    @ExcelProperty(value = "填报状态", index = 5) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private String statusName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "发送时间", index = 6) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "创建者名称")
    private String createUserName;

    @ApiModelProperty(value = "头像")
    private String createUserAvatar;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @ApiModelProperty(value = "逻辑删除（0：未删除  1：删除）")
    private DeletedEnum deleted;


}

