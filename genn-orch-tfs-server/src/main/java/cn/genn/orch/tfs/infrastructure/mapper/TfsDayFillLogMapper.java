package cn.genn.orch.tfs.infrastructure.mapper;

import cn.genn.orch.tfs.infrastructure.po.TfsDayFillLogPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */

public interface TfsDayFillLogMapper extends BaseMapper<TfsDayFillLogPO> {

    default TfsDayFillLogPO selectByOpenIdAndDay(String openId, LocalDate day){
        LambdaQueryWrapper<TfsDayFillLogPO> wrapper = Wrappers.lambdaQuery(TfsDayFillLogPO.class)
                .eq(TfsDayFillLogPO::getOpenId, openId)
                .eq(TfsDayFillLogPO::getSendDate, day)
                .last("limit 1");
        return selectOne(wrapper);
    }
}
